name: Deploy Astro site to GitHub Pages

on:
  push:
    branches: ["main"]
  workflow_dispatch:

# ✅ Allow GitHub Actions to push to gh-pages
permissions:
  contents: write
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build Astro project
        run: npm run build

      - name: Upload build output
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./dist  # ✅ Only upload built site

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
