---
import Layout from '../layouts/Layout.astro';
---

<Layout>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="container">
      <div class="nav-content">
        <div class="logo">
          <h2>AMIGO Living</h2>
          <span class="logo-tagline">For Ladies</span>
        </div>
        <div class="nav-links">
          <a href="#home">Home</a>
          <a href="#gallery">Gallery</a>
          <a href="#facilities">Facilities</a>
          <a href="#rooms">Rooms</a>
          <a href="#location">Location</a>
          <a href="#contact">Contact</a>
        </div>
        <div class="nav-cta">
          <a href="tel:+919744407313" class="btn-primary">Call Now</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero">
    <div class="hero-bg"></div>
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <div class="badge">Now Open</div>
          <h1>Premium Ladies PG in SG Palya Bangalore</h1>
          <p class="lead">Experience luxury living with AMIGO - Fully furnished rooms, 3 times delicious meals, and all modern amenities in the heart of Bangalore.</p>
          <div class="hero-features">
            <div class="feature-item">
              <span class="icon">🏠</span>
              <span>Fully Furnished</span>
            </div>
            <div class="feature-item">
              <span class="icon">🍽️</span>
              <span>3 Times Meals</span>
            </div>
            <div class="feature-item">
              <span class="icon">🔒</span>
              <span>24/7 Security</span>
            </div>
          </div>
          <div class="hero-cta">
            <a href="#contact" class="btn-primary">Book Your Room</a>
            <a href="tel:+919744407313" class="btn-secondary">Call Now</a>
          </div>
        </div>
        <div class="hero-image">
          <div class="hero-card">
            <div class="card-header">
              <h3>We Understand Your Needs</h3>
            </div>
            <div class="room-types">
              <div class="room-type">Single Sharing</div>
              <div class="room-type">Double Sharing</div>
              <div class="room-type">Triple Sharing</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- Facilities Section -->
  <section id="facilities" class="facilities section-padding">
    <div class="container">
      <div class="section-header">
        <h2>World-Class Facilities</h2>
        <p>Everything you need for comfortable and secure living</p>
      </div>
      <div class="facilities-grid">
        <div class="facility-card">
          <div class="facility-icon">🍽️</div>
          <h3>3 Times Delicious Meals</h3>
          <p>Kerala, North & South Indian cuisine prepared fresh daily</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">📹</div>
          <h3>24/7 Tech Security</h3>
          <p>Advanced security cameras and monitoring systems</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">📶</div>
          <h3>High Speed WiFi</h3>
          <p>Unlimited high-speed internet throughout the building</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">🧹</div>
          <h3>Professional Housekeeping</h3>
          <p>Daily cleaning and maintenance services</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">🏠</div>
          <h3>Common Appliances</h3>
          <p>Shared kitchen appliances and entertainment systems</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">🚿</div>
          <h3>Hot & Cool Water</h3>
          <p>24/7 hot and cold water supply</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">👔</div>
          <h3>Washing Machine</h3>
          <p>Modern washing machines for your convenience</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">❄️</div>
          <h3>Refrigerator</h3>
          <p>Common refrigerator for food storage</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">⚡</div>
          <h3>Power Backup</h3>
          <p>Uninterrupted power supply with backup generators</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">🛗</div>
          <h3>Lift Access</h3>
          <p>Elevator access to all floors</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">🔥</div>
          <h3>Hot Water</h3>
          <p>Instant hot water in all bathrooms</p>
        </div>
        <div class="facility-card">
          <div class="facility-icon">👆</div>
          <h3>Fingerprint Access</h3>
          <p>Secure biometric entry system</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Rooms Section -->
  <section id="rooms" class="rooms section-padding">
    <div class="container">
      <div class="section-header">
        <h2>Choose Your Perfect Room</h2>
        <p>Fully furnished luxury accommodations tailored to your needs</p>
      </div>
      <div class="rooms-grid">
        <div class="room-card">
          <div class="room-header">
            <h3>Single Sharing</h3>
            <div class="room-price">Contact for Pricing</div>
          </div>
          <div class="room-features">
            <div class="feature">✓ Private room with attached bathroom</div>
            <div class="feature">✓ Study table and comfortable bed</div>
            <div class="feature">✓ Wardrobe and storage space</div>
            <div class="feature">✓ All meals included</div>
            <div class="feature">✓ WiFi and all amenities</div>
          </div>
          <a href="#contact" class="btn-primary">Inquire Now</a>
        </div>
        <div class="room-card featured">
          <div class="popular-badge">Most Popular</div>
          <div class="room-header">
            <h3>Double Sharing</h3>
            <div class="room-price">Contact for Pricing</div>
          </div>
          <div class="room-features">
            <div class="feature">✓ Shared room with one roommate</div>
            <div class="feature">✓ Individual beds and study areas</div>
            <div class="feature">✓ Shared wardrobe space</div>
            <div class="feature">✓ All meals included</div>
            <div class="feature">✓ WiFi and all amenities</div>
          </div>
          <a href="#contact" class="btn-primary">Inquire Now</a>
        </div>
        <div class="room-card">
          <div class="room-header">
            <h3>Triple Sharing</h3>
            <div class="room-price">Contact for Pricing</div>
          </div>
          <div class="room-features">
            <div class="feature">✓ Shared room with two roommates</div>
            <div class="feature">✓ Individual beds and study spaces</div>
            <div class="feature">✓ Adequate storage for everyone</div>
            <div class="feature">✓ All meals included</div>
            <div class="feature">✓ WiFi and all amenities</div>
          </div>
          <a href="#contact" class="btn-primary">Inquire Now</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Location Section -->
  <section id="location" class="location section-padding">
    <div class="container">
      <div class="section-header">
        <h2>Prime Location in SG Palya</h2>
        <p>Strategically located near top educational institutions and shopping centers</p>
      </div>
      <div class="location-content">
        <div class="location-info">
          <h3>Nearby Attractions</h3>
          <div class="nearby-places">
            <div class="place-item">
              <span class="icon">🎓</span>
              <div>
                <h4>Christ University</h4>
                <p>Premium educational institution nearby</p>
              </div>
            </div>
            <div class="place-item">
              <span class="icon">🏢</span>
              <div>
                <h4>IBC Knowledge Park</h4>
                <p>IT hub and business center</p>
              </div>
            </div>
            <div class="place-item">
              <span class="icon">🎓</span>
              <div>
                <h4>St. Joseph College</h4>
                <p>Renowned educational institution</p>
              </div>
            </div>
            <div class="place-item">
              <span class="icon">🛍️</span>
              <div>
                <h4>Forum Mall</h4>
                <p>Shopping and entertainment complex</p>
              </div>
            </div>
            <div class="place-item">
              <span class="icon">🎭</span>
              <div>
                <h4>Sreenivasa Theatre</h4>
                <p>Entertainment and cultural venue</p>
              </div>
            </div>
          </div>
        </div>
        <div class="location-map">
          <div class="map-placeholder">
            <h4>📍 SG Palya, Bangalore</h4>
            <p>Exact location details will be shared upon inquiry</p>
            <a href="#contact" class="btn-secondary">Get Directions</a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact section-padding">
    <div class="container">
      <div class="section-header">
        <h2>Get in Touch</h2>
        <p>Ready to make AMIGO Living your new home? Contact us today!</p>
      </div>
      <div class="contact-content">
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">📞</div>
            <div>
              <h4>Phone Numbers</h4>
              <p><a href="tel:+919744407313">+91 97444 07313</a></p>
              <p><a href="tel:+************">+91 94005 72070</a></p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📧</div>
            <div>
              <h4>Email</h4>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📍</div>
            <div>
              <h4>Address</h4>
              <p>AMIGO Living<br>SG Palya, Bangalore<br>Karnataka, India</p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">🕒</div>
            <div>
              <h4>Visiting Hours</h4>
              <p>Monday - Sunday<br>9:00 AM - 8:00 PM</p>
            </div>
          </div>
        </div>
        <div class="contact-form">
          <form class="inquiry-form">
            <h3>Quick Inquiry</h3>
            <div class="form-group">
              <input type="text" placeholder="Your Name" required>
            </div>
            <div class="form-group">
              <input type="tel" placeholder="Phone Number" required>
            </div>
            <div class="form-group">
              <input type="email" placeholder="Email Address" required>
            </div>
            <div class="form-group">
              <select required>
                <option value="">Room Type Preference</option>
                <option value="single">Single Sharing</option>
                <option value="double">Double Sharing</option>
                <option value="triple">Triple Sharing</option>
              </select>
            </div>
            <div class="form-group">
              <textarea placeholder="Your Message (Optional)" rows="4"></textarea>
            </div>
            <button type="submit" class="btn-primary">Send Inquiry</button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Gallery Section -->
  <section id="gallery" class="gallery section-padding">
    <div class="container">
      <div class="section-header">
        <h2>Experience AMIGO Living</h2>
        <p>Take a look at our beautiful spaces and facilities</p>
      </div>

      <!-- Image Carousel -->
      <div class="carousel-container">
        <div class="carousel-wrapper">
          <div class="carousel-track" id="carouselTrack">
            <div class="carousel-slide active">
              <img src="/AM1.png" alt="AMIGO Living - Premium Room Interior" loading="lazy">
              <div class="slide-info">
                <h4>Premium Rooms</h4>
                <p>Fully furnished with modern amenities and comfortable bedding</p>
              </div>
            </div>
            <div class="carousel-slide">
              <img src="/AM2.png" alt="AMIGO Living - Common Area" loading="lazy">
              <div class="slide-info">
                <h4>Common Areas</h4>
                <p>Spacious and comfortable shared spaces for relaxation</p>
              </div>
            </div>
            <div class="carousel-slide">
              <img src="/AM3.png" alt="AMIGO Living - Dining Area" loading="lazy">
              <div class="slide-info">
                <h4>Dining Area</h4>
                <p>Clean and hygienic dining facilities with delicious meals</p>
              </div>
            </div>
            <div class="carousel-slide">
              <img src="/AM4.png" alt="AMIGO Living - Building Exterior" loading="lazy">
              <div class="slide-info">
                <h4>Building Exterior</h4>
                <p>Modern and secure building design in prime location</p>
              </div>
            </div>
            <div class="carousel-slide">
              <img src="/AM5.png" alt="AMIGO Living - Additional Facilities" loading="lazy">
              <div class="slide-info">
                <h4>Additional Facilities</h4>
                <p>More amenities and comfortable living spaces</p>
              </div>
            </div>
          </div>

          <!-- Carousel Controls -->
          <button class="carousel-btn prev-btn" id="prevBtn">‹</button>
          <button class="carousel-btn next-btn" id="nextBtn">›</button>
        </div>

        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
          <button class="indicator active" data-slide="0"></button>
          <button class="indicator" data-slide="1"></button>
          <button class="indicator" data-slide="2"></button>
          <button class="indicator" data-slide="3"></button>
          <button class="indicator" data-slide="4"></button>
        </div>
      </div>

      <!-- Video Section -->
      <div class="video-section">
        <h3>Virtual Tour</h3>
        <p>Watch our video tour to get a better feel of AMIGO Living</p>
        <div class="video-container">
          <video controls poster="/AM1.png" class="main-video">
            <source src="/AMIGOVIDEO.mp4" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>AMIGO Living</h3>
          <p>Premium ladies PG accommodation in Bangalore, offering comfort, security, and all modern amenities.</p>
        </div>
        <div class="footer-section">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#gallery">Gallery</a></li>
            <li><a href="#facilities">Facilities</a></li>
            <li><a href="#rooms">Rooms</a></li>
            <li><a href="#location">Location</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4>Contact Info</h4>
          <p>📞 +91 97444 07313</p>
          <p>📞 +91 94005 72070</p>
          <p>📧 <EMAIL></p>
          <p>📍 SG Palya, Bangalore</p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 AMIGO Living. All rights reserved. | Premium Ladies PG in Bangalore</p>
      </div>
    </div>
  </footer>
</Layout>

<style>
  /* Navigation */
  .navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
  }

  .logo h2 {
    color: #6B46C1;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
  }

  .logo-tagline {
    color: #F59E0B;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .nav-links {
    display: flex;
    gap: 2rem;
  }

  .nav-links a {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .nav-links a:hover {
    color: #6B46C1;
  }

  /* Hero Section */
  .hero {
    position: relative;
    padding: 8rem 0 6rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
  }

  .hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1') center/cover;
    opacity: 0.1;
  }

  .hero-content {
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
  }

  .badge {
    display: inline-block;
    background: rgba(245, 158, 11, 0.2);
    color: #F59E0B;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .hero h1 {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
  }

  .lead {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }

  .hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
  }

  .feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
  }

  .feature-item .icon {
    font-size: 1.25rem;
  }

  .hero-cta {
    display: flex;
    gap: 1rem;
  }

  .hero-card {
    background: rgba(255, 255, 255, 0.95);
    color: #1f2937;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }

  .card-header h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #6B46C1;
  }

  .room-types {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .room-type {
    background: linear-gradient(135deg, #6B46C1 0%, #9333EA 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
    font-weight: 600;
  }

  /* Gallery Section */
  .gallery {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  /* Carousel Styles */
  .carousel-container {
    margin-bottom: 4rem;
  }

  .carousel-wrapper {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .carousel-track {
    display: flex;
    transition: transform 0.5s ease-in-out;
  }

  .carousel-slide {
    min-width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .carousel-slide img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
  }

  .slide-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
  }

  .slide-info h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .slide-info p {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.4;
  }

  .carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(107, 70, 193, 0.8);
    color: white;
    border: none;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
  }

  .carousel-btn:hover {
    background: rgba(107, 70, 193, 1);
    transform: translateY(-50%) scale(1.1);
  }

  .prev-btn {
    left: 1rem;
  }

  .next-btn {
    right: 1rem;
  }

  .carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }

  .indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(107, 70, 193, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .indicator.active {
    background: #6B46C1;
    transform: scale(1.2);
  }

  .indicator:hover {
    background: rgba(107, 70, 193, 0.6);
  }

  /* Video Section */
  .video-section {
    text-align: center;
    margin-top: 4rem;
  }

  .video-section h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .video-section p {
    color: #6b7280;
    font-size: 1.125rem;
    margin-bottom: 2rem;
  }

  .video-container {
    max-width: 600px;
    margin: 0 auto;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    background: #000;
  }

  .main-video {
    width: 100%;
    height: auto;
    display: block;
  }

  /* Facilities Section */
  .facilities {
    background: #f9fafb;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .section-header p {
    font-size: 1.125rem;
    color: #6b7280;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
  }

  .facility-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .facility-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  .facility-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .facility-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }

  .facility-card p {
    color: #6b7280;
  }

  /* Rooms Section */
  .rooms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
  }

  .room-card {
    position: relative;
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .room-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }

  .room-card.featured {
    border: 2px solid #6B46C1;
    transform: scale(1.05);
  }

  .popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #F59E0B;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .room-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .room-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }

  .room-price {
    font-size: 1.125rem;
    color: #6B46C1;
    font-weight: 600;
  }

  .room-features {
    margin-bottom: 2rem;
  }

  .feature {
    padding: 0.5rem 0;
    color: #374151;
  }

  /* Location Section */
  .location {
    background: #f9fafb;
  }

  .location-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }

  .nearby-places {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .place-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .place-item .icon {
    font-size: 2rem;
    width: 3rem;
    text-align: center;
  }

  .place-item h4 {
    margin-bottom: 0.25rem;
    color: #1f2937;
  }

  .place-item p {
    color: #6b7280;
    font-size: 0.875rem;
  }

  .map-placeholder {
    background: white;
    padding: 3rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .map-placeholder h4 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #1f2937;
  }

  .map-placeholder p {
    color: #6b7280;
    margin-bottom: 2rem;
  }

  /* Contact Section */
  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }

  .contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .contact-icon {
    font-size: 1.5rem;
    background: #6B46C1;
    color: white;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .contact-item h4 {
    margin-bottom: 0.5rem;
    color: #1f2937;
  }

  .contact-item p {
    color: #6b7280;
  }

  .contact-item a {
    color: #6B46C1;
    text-decoration: none;
  }

  .contact-item a:hover {
    text-decoration: underline;
  }

  .inquiry-form {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  .inquiry-form h3 {
    margin-bottom: 2rem;
    color: #1f2937;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 0.875rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #6B46C1;
    box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.1);
  }

  /* Footer */
  .footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .footer-section h3,
  .footer-section h4 {
    margin-bottom: 1rem;
    color: #F59E0B;
  }

  .footer-section ul {
    list-style: none;
  }

  .footer-section ul li {
    margin-bottom: 0.5rem;
  }

  .footer-section a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .footer-section a:hover {
    color: #F59E0B;
  }

  .footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
  }

  /* Mobile Responsiveness */
  @media (max-width: 768px) {
    .nav-links {
      display: none;
    }

    .hero h1 {
      font-size: 2rem;
    }

    .hero-content {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .hero-features {
      flex-direction: column;
      gap: 1rem;
    }

    .hero-cta {
      flex-direction: column;
    }

    .carousel-slide img {
      height: 250px;
    }

    .carousel-btn {
      width: 2.5rem;
      height: 2.5rem;
      font-size: 1.2rem;
    }

    .prev-btn {
      left: 0.5rem;
    }

    .next-btn {
      right: 0.5rem;
    }

    .slide-info {
      padding: 1rem;
    }

    .slide-info h4 {
      font-size: 1.2rem;
    }

    .slide-info p {
      font-size: 0.9rem;
    }

    .location-content,
    .contact-content {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .facilities-grid,
    .rooms-grid,
    .gallery-grid {
      grid-template-columns: 1fr;
    }

    .room-card.featured {
      transform: none;
    }
  }

  /* Animation */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in {
    animation: fadeInUp 0.6s ease forwards;
  }
</style>

<script>
  // Smooth scrolling for navigation links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Form submission (placeholder - integrate with your backend)
  document.querySelector('.inquiry-form').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('Thank you for your inquiry! We will contact you soon.');
    this.reset();
  });

  // Carousel functionality
  document.addEventListener('DOMContentLoaded', function() {
    const track = document.getElementById('carouselTrack');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const indicators = document.querySelectorAll('.indicator');

    if (!track || !prevBtn || !nextBtn) return;

    let currentSlide = 0;
    const totalSlides = document.querySelectorAll('.carousel-slide').length;

    function updateCarousel() {
      const translateX = -currentSlide * 100;
      track.style.transform = `translateX(${translateX}%)`;

      // Update indicators
      indicators.forEach((indicator, index) => {
        indicator.classList.toggle('active', index === currentSlide);
      });
    }

    function nextSlide() {
      currentSlide = (currentSlide + 1) % totalSlides;
      updateCarousel();
    }

    function prevSlide() {
      currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
      updateCarousel();
    }

    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);

    // Indicator clicks
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        currentSlide = index;
        updateCarousel();
      });
    });

    // Auto-play carousel
    let autoPlayInterval = setInterval(nextSlide, 5000);

    // Pause auto-play on hover
    const carouselWrapper = document.querySelector('.carousel-wrapper');
    if (carouselWrapper) {
      carouselWrapper.addEventListener('mouseenter', () => {
        clearInterval(autoPlayInterval);
      });

      carouselWrapper.addEventListener('mouseleave', () => {
        autoPlayInterval = setInterval(nextSlide, 5000);
      });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') prevSlide();
      if (e.key === 'ArrowRight') nextSlide();
    });
  });

  // Navbar scroll effect
  window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
      navbar.style.background = 'rgba(255, 255, 255, 0.98)';
      navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
      navbar.style.background = 'rgba(255, 255, 255, 0.95)';
      navbar.style.boxShadow = 'none';
    }
  });
</script>